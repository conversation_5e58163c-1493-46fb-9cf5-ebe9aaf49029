package innerapi

import (
	"deskcrm/api/achilles"
	"deskcrm/api/dal"
	"deskcrm/api/examcore"
	"deskcrm/api/moat"
	"deskcrm/components"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

var ExamService examService

type examService struct {
}

// RegistTestInfo 摸底测信息
// 对应PHP版本的AssistantDesk_InterviewReferLpc::registTest返回结构
type RegistTestInfo struct {
	IsFinish   int    `json:"isFinish"`   // 是否完成 0-未完成 1-已完成
	CostTime   string `json:"costTime"`   // 耗时
	Score      int    `json:"score"`      // 分数
	FinishTime string `json:"finishTime"` // 完成时间
}

// GetCpuBindExams CPU试卷绑定情况
func (s examService) GetCpuBindExams(ctx *gin.Context, cpuIds []int64, bindType int) (map[int64]map[int64]examcore.BindInfo, error) {
	if len(cpuIds) == 0 {
		return make(map[int64]map[int64]examcore.BindInfo), nil
	}

	// 1. 构建绑定字符串列表
	var bindList []string
	for _, cpuId := range cpuIds {
		bindKey := fmt.Sprintf("cpu_%d:%d", cpuId, bindType)
		bindList = append(bindList, bindKey)
	}

	// 2. 兼容试卷绑定到大纲上
	enhancedBindList, outlineLessonMap, err := s.GetOutlineBindList(ctx, bindList)
	if err != nil {
		zlog.Warnf(ctx, "GetOutlineBindList failed: %v", err)
		// 如果获取outline绑定失败，继续使用原始bindList
		enhancedBindList = bindList
		outlineLessonMap = make(map[string]*achilles.ProcessedLessonInfo)
	}

	// 3. 获取试卷绑定关系
	examcoreClient := examcore.NewClient()
	examRelationList, err := examcoreClient.GetRelation(ctx, enhancedBindList)
	if err != nil {
		zlog.Warnf(ctx, "cpu试卷绑定信息获取失败: %v", err)
		return nil, err
	}

	// 4. 处理返回结果，构建以cpuId为key的数组
	cpuBindExams := make(map[int64]map[int64]examcore.BindInfo)
	for _, cpuId := range cpuIds {
		bindKey := fmt.Sprintf("cpu_%d:%d", cpuId, bindType)
		if examList, exists := examRelationList[bindKey]; exists && len(examList) > 0 {
			cpuBindExams[cpuId] = examList
		}
	}

	// 5. 处理outline兼容逻辑
	if len(outlineLessonMap) > 0 {
		cpuBindExams = s.processOutlineRelation(cpuBindExams, examRelationList, outlineLessonMap)
	}

	return cpuBindExams, nil
}

// GetOutlineBindList bindList兼容outline绑定新方案
func (s examService) GetOutlineBindList(ctx *gin.Context, bindList []string) ([]string, map[string]*achilles.ProcessedLessonInfo, error) {
	// 1. 提取lesson相关的绑定，同时记录bindType信息
	var lessonIds []string
	lessonBindMap := make(map[string]string) // lessonId -> bindType的映射
	for _, bindKey := range bindList {
		if strings.Contains(bindKey, "lesson") {
			// 解析格式: lesson_123:456
			parts := strings.Split(bindKey, ":")
			if len(parts) == 2 {
				lessonPart := strings.Split(parts[0], "_")
				if len(lessonPart) == 2 {
					lessonIds = append(lessonIds, lessonPart[1])
					lessonBindMap[lessonPart[1]] = parts[1]
				}
			}
		}
	}

	// 2. 如果没有lesson绑定，直接返回原始bindList
	if len(lessonIds) == 0 {
		return bindList, make(map[string]*achilles.ProcessedLessonInfo), nil
	}

	// 3. 获取lesson信息，包含outlineId
	achillesClient := achilles.NewClient()
	lessonInfoMap, err := achillesClient.GetInfoByLessonId(ctx, lessonIds, []string{"lessonId", "outlineId"})
	if err != nil {
		zlog.Warnf(ctx, "获取lesson信息失败: %v", err)
		return bindList, make(map[string]*achilles.ProcessedLessonInfo), err
	}

	// 4. 构建lessonInfo映射，用于后续处理
	lessonInfoByIdMap := make(map[string]*achilles.ProcessedLessonInfo)
	for lessonIdStr, lessonInfo := range lessonInfoMap {
		lessonInfoByIdMap[lessonIdStr] = lessonInfo
	}

	// 5. 为每个lesson绑定添加对应的outline绑定
	for _, lessonId := range lessonIds {
		if lessonInfo, exists := lessonInfoByIdMap[lessonId]; exists {
			bindType := lessonBindMap[lessonId]
			outlineId := lessonInfo.OutlineId
			outlineBindKey := fmt.Sprintf("outline_%d:%s", outlineId, bindType)
			bindList = append(bindList, outlineBindKey)
		}
	}

	return bindList, lessonInfoByIdMap, nil
}

// processOutlineRelation 处理outline绑定兼容逻辑
func (s examService) processOutlineRelation(cpuBindExams map[int64]map[int64]examcore.BindInfo, examRelationList map[string]map[int64]examcore.BindInfo, outlineLessonMap map[string]*achilles.ProcessedLessonInfo) map[int64]map[int64]examcore.BindInfo {
	if len(examRelationList) == 0 || len(outlineLessonMap) == 0 {
		return cpuBindExams
	}

	// 遍历examRelationList，查找outline绑定并转换为lesson绑定
	for bindKey, relation := range examRelationList {
		// 检查是否为outline绑定（格式：outline_123:456）
		if strings.Contains(bindKey, "outline") {
			// 提取outlineId：从"outline_123:456"中提取"123"
			parts := strings.Split(bindKey, ":")
			if len(parts) != 2 {
				continue
			}

			bindPart := parts[0] // "outline_123"
			bindType := parts[1] // "456"

			outlineIdParts := strings.Split(bindPart, "_")
			if len(outlineIdParts) != 2 || outlineIdParts[0] != "outline" {
				continue
			}

			outlineIdStr := outlineIdParts[1] // "123"
			outlineId, err := strconv.Atoi(outlineIdStr)
			if err != nil {
				zlog.Warnf(nil, "processOutlineRelation: 解析outlineId失败: %s, err: %v", outlineIdStr, err)
				continue
			}

			// 在outlineLessonMap中查找对应的lesson
			for _, lessonInfo := range outlineLessonMap {
				if lessonInfo.OutlineId == outlineId {
					// 构建lesson绑定key：lesson_456:789
					lessonKey := fmt.Sprintf("lesson_%d:%s", lessonInfo.LessonId, bindType)

					// 如果lesson绑定不存在，则添加（避免覆盖已有的lesson绑定）
					if _, exists := examRelationList[lessonKey]; !exists {
						examRelationList[lessonKey] = relation
						zlog.Debugf(nil, "processOutlineRelation: 添加lesson绑定 %s -> %s", bindKey, lessonKey)
					}
				}
			}
		}
	}
	return cpuBindExams
}

// RegistTest 获取摸底测信息
// 对应PHP版本的AssistantDesk_InterviewReferLpc::registTest方法
func (s examService) RegistTest(ctx *gin.Context, examId int64, studentUid int64) (*RegistTestInfo, error) {
	// 1. 参数验证
	if examId <= 0 || studentUid <= 0 {
		zlog.Warnf(ctx, "RegistTest 参数错误: examId=%d, studentUid=%d", examId, studentUid)
		return nil, fmt.Errorf("参数错误")
	}

	// 2. 初始化返回结构
	registTest := &RegistTestInfo{
		IsFinish:   0,
		CostTime:   "",
		Score:      0,
		FinishTime: "",
	}

	// 3. 获取学生试卷作答信息
	examcoreClient := examcore.NewClient()
	answerKey := fmt.Sprintf("%d_%d", examId, studentUid)
	answers, err := examcoreClient.GetAnswer(ctx, []string{answerKey})
	if err != nil {
		zlog.Warnf(ctx, "RegistTest 获取作答信息失败: examId=%d, studentUid=%d, err=%v", examId, studentUid, err)
		return registTest, nil // 返回默认值，不返回错误
	}

	// 4. 检查是否有作答记录
	answerList, exists := answers[answerKey]
	if !exists || len(answerList) == 0 {
		// 没有作答记录，返回默认的未完成状态
		return registTest, nil
	}

	// 5. 取最后一次作答记录
	lastAnswer := answerList[len(answerList)-1]

	// 6. 更新完成状态
	registTest.IsFinish = 1

	// 7. 格式化耗时
	if lastAnswer.Props.Duration > 0 {
		registTest.CostTime = components.Util.FormatRemainTime(int64(lastAnswer.Props.Duration), true)
	} else {
		registTest.CostTime = "0"
	}

	// 8. 设置分数
	registTest.Score = lastAnswer.Score

	// 9. 格式化完成时间
	if lastAnswer.CreateTime > 0 {
		registTest.FinishTime = time.Unix(int64(lastAnswer.CreateTime), 0).Format("2006-01-02 15:04:05")
	}

	return registTest, nil
}

// ExamTestItem 试卷测试项目结构
// 对应PHP版本的返回结构
type ExamTestItem struct {
	ExamId        int64       `json:"examId"`        // 试卷ID
	TestType      string      `json:"testType"`      // 测试类型：摸底测/阶段测
	ExamCount     int         `json:"examCount"`     // 题目数量
	PassRule      string      `json:"passRule"`      // 通过规则
	MaxAnswerTime string      `json:"maxAnswerTime"` // 最大答题时间
	AnswerTime    string      `json:"answerTime"`    // 实际答题时间
	RightCount    interface{} `json:"rightCount"`    // 正确题目数量，可能是数字或"-"
	Score         interface{} `json:"score"`         // 分数，可能是数字或"-"
	Department    int         `json:"department"`    // 学段：1-学前，2-小学，3-初中，4-高中
	Grade         int         `json:"grade"`         // 年级
	Subject       int         `json:"subject"`       // 学科
	LessonId      int         `json:"lessonId"`      // 章节ID，摸底测为0
	CpuId         int64       `json:"cpuId"`         // CPU ID
	ExamType      int         `json:"examType"`      // 试卷类型
	ReportUrl     string      `json:"reportUrl"`     // 报告链接
	HasAnswered   bool        `json:"hasAnswered"`   // 是否已答题
}

const (
	JDCH = 4 // 报后测常量，对应PHP中的self::JDCH
)

// GetExamTestList 获取学生试卷测试列表
// 对应PHP版本的Service_Page_DeskV1_Student_GetExamTestList::execute方法
func (s examService) GetExamTestList(ctx *gin.Context, studentUid int64, courseId int64) ([]ExamTestItem, error) {
	// 1. 参数验证
	if studentUid <= 0 || courseId <= 0 {
		zlog.Errorf(ctx, "GetExamTestList 参数错误: studentUid=%d, courseId=%d", studentUid, courseId)
		return nil, components.ErrorParamInvalid
	}

	// 2. 获取课程数据
	courseInfo, lessonIds, cpuId, err := s.getCourseData(ctx, courseId)
	if err != nil {
		return nil, err
	}
	components.Debugf(ctx, "GetExamTestList getCourseData completed, courseInfo: %s, lessonIds: %v, cpuId: %d", courseInfo, lessonIds, cpuId)

	// 3. 获取摸底测试数据
	urgeBottomTestBindStr, urgeBottomTestRelation, urgeBottomTestExamIds, urgeBottomTestExamInfo, err := s.getUrgeBottomTestData(ctx, cpuId)
	if err != nil {
		return nil, err
	}
	components.Debugf(ctx, "GetExamTestList getUrgeBottomTestData completed, urgeBottomTestBindStr: %s, urgeBottomTestRelation: %v, urgeBottomTestExamIds: %v, urgeBottomTestExamInfo: %v", urgeBottomTestBindStr, urgeBottomTestRelation, urgeBottomTestExamIds, urgeBottomTestExamInfo)

	// 4. 获取阶段测试数据
	stageExamIds, stageRelation, stageExamInfo, examIdBindKey, err := s.getStageData(ctx, lessonIds)
	if err != nil {
		return nil, err
	}
	components.Debugf(ctx, "GetExamTestList getStageData completed, stageExamIds: %v, stageRelation: %v, stageExamInfo: %v, examIdBindKey: %v", stageExamIds, stageRelation, stageExamInfo, examIdBindKey)

	// 5. 获取答案信息
	answerInfo, err := s.getAnswers(ctx, urgeBottomTestExamIds, stageExamIds, studentUid)
	if err != nil {
		return nil, err
	}
	components.Debugf(ctx, "GetExamTestList getAnswers completed, answerInfo: %v", answerInfo)

	// 6. 格式化结果
	var examList []ExamTestItem

	// 格式化摸底测试
	urgeBottomTestList := s.formatUrgeBottomTest(ctx, urgeBottomTestExamInfo, urgeBottomTestRelation, urgeBottomTestBindStr, answerInfo, studentUid, courseId, cpuId, courseInfo)
	examList = append(examList, urgeBottomTestList...)

	// 格式化阶段测试
	stageTestList := s.formatStageTest(ctx, stageExamInfo, stageRelation, examIdBindKey, answerInfo, studentUid, cpuId)
	examList = append(examList, stageTestList...)

	// 添加最终结果日志
	zlog.Infof(ctx, "GetExamTestList 最终结果: urgeBottomTestList数量=%d, stageTestList数量=%d, 总数量=%d",
		len(urgeBottomTestList), len(stageTestList), len(examList))

	if len(examList) == 0 {
		zlog.Warnf(ctx, "GetExamTestList 返回空结果: studentUid=%d, courseId=%d, lessonIds=%v, cpuId=%d",
			studentUid, courseId, lessonIds, cpuId)
	} else {
		for i, item := range examList {
			zlog.Infof(ctx, "GetExamTestList 结果[%d]: examId=%d, testType=%s, lessonId=%d, hasAnswered=%t",
				i, item.ExamId, item.TestType, item.LessonId, item.HasAnswered)
		}
	}

	return examList, nil
}

// getCourseData 获取课程数据
// 对应PHP版本的getCourseData方法
func (s examService) getCourseData(ctx *gin.Context, courseId int64) (dal.CourseInfo, []int, int64, error) {
	// 调用Dal API获取课程和章节信息
	courseFields := []string{"courseId", "courseName", "cpuId", "year"}
	lessonFields := []string{"lessonId", "outlineId"}

	courseInfoMap, err := dal.GetCourseLessonInfoByCourseIdsAndFields(ctx, []int64{courseId}, courseFields, lessonFields)
	if err != nil {
		zlog.Warnf(ctx, "getCourseData 获取课程信息失败: courseId=%d, err=%v", courseId, err)
		return dal.CourseInfo{}, nil, 0, fmt.Errorf("获取课程信息失败")
	}

	// 添加调试信息：打印API返回的所有key
	zlog.Infof(ctx, "getCourseData API返回的courseInfoMap keys: %v", getCourseMapKeys(courseInfoMap))

	// 尝试多种可能的key格式查找课程信息
	var courseInfo dal.CourseInfo
	var exists bool

	// 尝试格式1: 字符串格式的courseId
	courseIdStr := strconv.FormatInt(courseId, 10)
	if courseInfo, exists = courseInfoMap[courseIdStr]; exists {
		zlog.Debugf(ctx, "getCourseData 找到课程信息，使用key格式: %s", courseIdStr)
	} else {
		// 尝试格式2: 直接遍历查找匹配的courseId
		for key, info := range courseInfoMap {
			if int64(info.CourseId) == courseId {
				courseInfo = info
				exists = true
				zlog.Debugf(ctx, "getCourseData 找到课程信息，通过遍历匹配，key=%s, courseId=%d", key, info.CourseId)
				break
			}
		}
	}

	if !exists {
		zlog.Warnf(ctx, "getCourseData 课程不存在: courseId=%d, 尝试的key=%s, 可用keys=%v", courseId, courseIdStr, getCourseMapKeys(courseInfoMap))
		return dal.CourseInfo{}, nil, 0, fmt.Errorf("课程不存在")
	}

	// 提取lessonIds
	var lessonIds []int
	if courseInfo.LessonList != nil {
		zlog.Infof(ctx, "getCourseData LessonList不为空，包含%d个lesson", len(courseInfo.LessonList))
		for lessonIdStr := range courseInfo.LessonList {
			if lessonId, err := strconv.Atoi(lessonIdStr); err == nil {
				lessonIds = append(lessonIds, lessonId)
				zlog.Debugf(ctx, "getCourseData 添加lessonId: %s -> %d", lessonIdStr, lessonId)
			} else {
				zlog.Warnf(ctx, "getCourseData 无法解析lessonId: %s, err: %v", lessonIdStr, err)
			}
		}
	} else {
		zlog.Warnf(ctx, "getCourseData LessonList为空: courseId=%d", courseId)
	}

	cpuId := courseInfo.CpuId

	zlog.Infof(ctx, "getCourseData 成功: courseId=%d, lessonIds=%v (数量=%d), cpuId=%d", courseId, lessonIds, len(lessonIds), cpuId)
	return courseInfo, lessonIds, cpuId, nil
}

// getUrgeBottomTestData 获取摸底测试数据
// 对应PHP版本的getUrgeBottomTestData方法
func (s examService) getUrgeBottomTestData(ctx *gin.Context, cpuId int64) (string, map[string]map[int64]examcore.BindInfo, []int64, map[int64]examcore.ExamDetailInfo, error) {
	// 1. 构建摸底测试绑定字符串
	urgeBottomTestBindStr := fmt.Sprintf("cpu_%d:%d", cpuId, examcore.ExamTypeSurvey)
	bindList := []string{urgeBottomTestBindStr}

	// 2. 获取绑定关系
	examcoreClient := examcore.NewClient()
	urgeBottomTestRelation, err := examcoreClient.GetRelation(ctx, bindList)
	if err != nil {
		zlog.Warnf(ctx, "getUrgeBottomTestData 获取绑定关系失败: cpuId=%d, err=%v", cpuId, err)
		return "", nil, nil, nil, err
	}

	// 3. 提取试卷ID
	var urgeBottomTestExamIds []int64
	if bindInfos, exists := urgeBottomTestRelation[urgeBottomTestBindStr]; exists {
		for _, bindInfo := range bindInfos {
			examId := cast.ToInt64(bindInfo.ExamId)
			urgeBottomTestExamIds = append(urgeBottomTestExamIds, examId)
		}
	}

	// 4. 获取试卷信息
	var urgeBottomTestExamInfo map[int64]examcore.ExamDetailInfo
	if len(urgeBottomTestExamIds) > 0 {
		urgeBottomTestExamInfo, err = examcoreClient.GetExamInfo(ctx, urgeBottomTestExamIds)
		if err != nil {
			zlog.Warnf(ctx, "getUrgeBottomTestData 获取试卷信息失败: examIds=%v, err=%v", urgeBottomTestExamIds, err)
			urgeBottomTestExamInfo = make(map[int64]examcore.ExamDetailInfo)
		}
	} else {
		urgeBottomTestExamInfo = make(map[int64]examcore.ExamDetailInfo)
	}

	return urgeBottomTestBindStr, urgeBottomTestRelation, urgeBottomTestExamIds, urgeBottomTestExamInfo, nil
}

// getStageData 获取阶段测试数据
// 对应PHP版本的getStageData方法
func (s examService) getStageData(ctx *gin.Context, lessonIds []int) ([]int64, map[string]map[int64]examcore.BindInfo, map[int64]examcore.ExamDetailInfo, map[int64]string, error) {
	zlog.Infof(ctx, "getStageData 开始处理: lessonIds=%v (数量=%d)", lessonIds, len(lessonIds))

	if len(lessonIds) == 0 {
		zlog.Warnf(ctx, "getStageData lessonIds为空，无法获取阶段测试数据")
		return []int64{}, make(map[string]map[int64]examcore.BindInfo), make(map[int64]examcore.ExamDetailInfo), make(map[int64]string), nil
	}

	// 1. 构建阶段测试绑定字符串列表
	var bindList []string
	for _, lessonId := range lessonIds {
		bindKey := fmt.Sprintf("lesson_%d:%d", lessonId, examcore.ExamTypeStage)
		bindList = append(bindList, bindKey)
		zlog.Debugf(ctx, "getStageData 构建bindKey: lessonId=%d -> %s", lessonId, bindKey)
	}

	zlog.Infof(ctx, "getStageData 构建的bindList: %v", bindList)

	// 2. 获取绑定关系
	examcoreClient := examcore.NewClient()
	stageRelation, err := examcoreClient.GetRelation(ctx, bindList)
	if err != nil {
		zlog.Warnf(ctx, "getStageData 获取绑定关系失败: lessonIds=%v, err=%v", lessonIds, err)
		return nil, nil, nil, nil, err
	}

	zlog.Infof(ctx, "getStageData 获取绑定关系成功: 返回%d个绑定关系", len(stageRelation))
	for bindKey, examInfos := range stageRelation {
		zlog.Debugf(ctx, "getStageData 绑定关系: %s -> %d个试卷", bindKey, len(examInfos))
	}

	// 3. 提取试卷ID并建立examId到bindKey的映射
	var stageExamIds []int64
	examIdBindKey := make(map[int64]string)

	for bindKey, examInfos := range stageRelation {
		var examIds []int64
		for _, examInfo := range examInfos {
			examId := cast.ToInt64(examInfo.ExamId)
			examIds = append(examIds, examId)
		}

		// 去重
		uniqueExamIds := make(map[int64]bool)
		for _, examId := range examIds {
			if !uniqueExamIds[examId] {
				uniqueExamIds[examId] = true
				stageExamIds = append(stageExamIds, examId)
				examIdBindKey[examId] = bindKey
			}
		}
	}

	zlog.Infof(ctx, "getStageData 提取到的stageExamIds: %v (数量=%d)", stageExamIds, len(stageExamIds))

	// 4. 获取试卷信息
	var stageExamInfo map[int64]examcore.ExamDetailInfo
	if len(stageExamIds) > 0 {
		stageExamInfo, err = examcoreClient.GetExamInfo(ctx, stageExamIds)
		if err != nil {
			zlog.Warnf(ctx, "getStageData 获取试卷信息失败: examIds=%v, err=%v", stageExamIds, err)
			stageExamInfo = make(map[int64]examcore.ExamDetailInfo)
		} else {
			zlog.Infof(ctx, "getStageData 获取试卷信息成功: 返回%d个试卷信息", len(stageExamInfo))
		}
	} else {
		zlog.Warnf(ctx, "getStageData stageExamIds为空，无法获取试卷信息")
		stageExamInfo = make(map[int64]examcore.ExamDetailInfo)
	}

	zlog.Infof(ctx, "getStageData 完成: stageExamIds数量=%d, stageExamInfo数量=%d", len(stageExamIds), len(stageExamInfo))
	return stageExamIds, stageRelation, stageExamInfo, examIdBindKey, nil
}

// getAnswers 获取答案信息
// 对应PHP版本的getAnswers方法
func (s examService) getAnswers(ctx *gin.Context, urgeBottomTestExamIds []int64, stageExamIds []int64, studentUid int64) (map[string][]examcore.AnswerInfo, error) {
	// 1. 合并所有试卷ID
	var allExamIds []int64
	allExamIds = append(allExamIds, urgeBottomTestExamIds...)
	allExamIds = append(allExamIds, stageExamIds...)

	if len(allExamIds) == 0 {
		return make(map[string][]examcore.AnswerInfo), nil
	}

	// 2. 构建answerKey列表
	var answerKeys []string
	for _, examId := range allExamIds {
		answerKey := fmt.Sprintf("%d_%d", examId, studentUid)
		answerKeys = append(answerKeys, answerKey)
	}

	// 3. 获取答案信息
	examcoreClient := examcore.NewClient()
	answerInfo, err := examcoreClient.GetAnswer(ctx, answerKeys)
	if err != nil {
		zlog.Warnf(ctx, "getAnswers 获取答案信息失败: answerKeys=%v, err=%v", answerKeys, err)
		return make(map[string][]examcore.AnswerInfo), nil // 返回空map，不返回错误
	}

	return answerInfo, nil
}

// formatUrgeBottomTest 格式化摸底测试数据
// 对应PHP版本的formatUrgeBottomTest方法
func (s examService) formatUrgeBottomTest(ctx *gin.Context, urgeBottomTestExamInfo map[int64]examcore.ExamDetailInfo,
	urgeBottomTestRelation map[string]map[int64]examcore.BindInfo, urgeBottomTestBindStr string,
	answerInfo map[string][]examcore.AnswerInfo, studentUid, courseId, cpuId int64, courseInfo dal.CourseInfo) []ExamTestItem {

	var examList []ExamTestItem

	for examId, examDetailInfo := range urgeBottomTestExamInfo {
		examInfo := examDetailInfo.Exam

		// 获取答案信息
		answerKey := fmt.Sprintf("%d_%d", examId, studentUid)
		var answerInfoList []examcore.AnswerInfo
		if answers, exists := answerInfo[answerKey]; exists {
			answerInfoList = answers
		}

		// 按创建时间倒序排序，取最新的答案
		var latestAnswer examcore.AnswerInfo
		var tAnswerTime int
		if len(answerInfoList) > 0 {
			// 按createTime降序排序
			sort.Slice(answerInfoList, func(i, j int) bool {
				return answerInfoList[i].CreateTime > answerInfoList[j].CreateTime
			})
			latestAnswer = answerInfoList[0]
			tAnswerTime = latestAnswer.Props.Duration
		}

		// 构建基础数据
		hasAnswer := len(answerInfoList) > 0
		item := ExamTestItem{
			ExamId:        examId,
			TestType:      "摸底测",
			ExamCount:     len(examInfo.TidList),
			PassRule:      s.getPassRule(examInfo.TotalScore, s.getPassScore(urgeBottomTestRelation, urgeBottomTestBindStr)),
			MaxAnswerTime: s.getMaxAnswerTime(examInfo.Props.Duration),
			AnswerTime:    s.getAnswerTime(tAnswerTime),
			RightCount:    s.getRightCount(latestAnswer.AnswerList),
			Score:         s.getScore(latestAnswer, hasAnswer),
			Department:    s.getDepartment(examInfo.Grade),
			Grade:         examInfo.Grade,
			Subject:       examInfo.Subject,
			LessonId:      0, // 摸底测的lessonId为0
			CpuId:         cpuId,
			ExamType:      examInfo.ExamType,
			ReportUrl:     "",
			HasAnswered:   tAnswerTime > 0,
		}

		// 处理报告链接
		// 对应PHP版本：摸底测报告只支持小学（包括学前）数学：2
		// if ($tmp['score'] !== '-' && in_array($tmp['grade'], [11, 12, 13, 14, 15, 16, 60, 61, 62, 63, 64]))
		if item.Score != "-" && s.isGradeSupported(examInfo.Grade) {
			const ENGLISH_SUBJECT = 3 // 对应PHP版本的Zb_Const_GradeSubject::ENGLISH

			if examInfo.Subject != ENGLISH_SUBJECT { // 非英语学科
				reportUrl := fmt.Sprintf("https://www.zuoyebang.com/static/hy/study-report/xs-exam-report.html?examId=%d&courseId=%d&uid=%d&index=0&type=%d&subject=%d",
					examId, courseId, studentUid, JDCH, examInfo.Subject)
				item.ReportUrl = s.getShortUrl(ctx, reportUrl)
			} else { // 英语学科
				// 对应PHP版本：$year = $this->courseInfo['year']; if ($year >= 2022)
				if courseInfo.Year >= 2022 {
					reportUrl := fmt.Sprintf("https://www.zuoyebang.com/static/hy/study-report/xs-exam-report.html?examId=%d&courseId=%d&uid=%d&index=0&type=%d&subject=%d",
						examId, courseId, studentUid, JDCH, examInfo.Subject)
					item.ReportUrl = s.getShortUrl(ctx, reportUrl)
				}
			}
		}

		examList = append(examList, item)
	}

	return examList
}

// formatStageTest 格式化阶段测试数据
// 对应PHP版本的formatStageTest方法
func (s examService) formatStageTest(ctx *gin.Context, stageExamInfo map[int64]examcore.ExamDetailInfo,
	stageRelation map[string]map[int64]examcore.BindInfo, examIdBindKey map[int64]string,
	answerInfo map[string][]examcore.AnswerInfo, studentUid, cpuId int64) []ExamTestItem {

	var examList []ExamTestItem

	for examId, examDetailInfo := range stageExamInfo {
		examInfo := examDetailInfo.Exam

		// 获取答案信息
		answerKey := fmt.Sprintf("%d_%d", examId, studentUid)
		var answerInfoList []examcore.AnswerInfo
		if answers, exists := answerInfo[answerKey]; exists {
			answerInfoList = answers
		}

		// 按创建时间倒序排序，取最新的答案
		var latestAnswer examcore.AnswerInfo
		var tAnswerTime int
		if len(answerInfoList) > 0 {
			// 按createTime降序排序
			sort.Slice(answerInfoList, func(i, j int) bool {
				return answerInfoList[i].CreateTime > answerInfoList[j].CreateTime
			})
			latestAnswer = answerInfoList[0]
			tAnswerTime = latestAnswer.Props.Duration
		}

		// 获取bindKey和lessonId
		bindKey := examIdBindKey[examId]
		lessonId := s.extractLessonIdFromBindKey(bindKey)

		// 构建基础数据
		hasAnswer := len(answerInfoList) > 0
		item := ExamTestItem{
			ExamId:        examId,
			TestType:      "阶段测",
			ExamCount:     len(examInfo.TidList),
			PassRule:      s.getPassRule(examInfo.TotalScore, s.getPassScoreFromStageRelation(stageRelation, bindKey)),
			MaxAnswerTime: s.getMaxAnswerTime(examInfo.Props.Duration),
			AnswerTime:    s.getAnswerTime(tAnswerTime),
			RightCount:    s.getRightCount(latestAnswer.AnswerList),
			Score:         s.getScore(latestAnswer, hasAnswer),
			Department:    s.getDepartment(examInfo.Grade),
			Grade:         examInfo.Grade,
			Subject:       examInfo.Subject,
			LessonId:      lessonId,
			CpuId:         cpuId,
			ExamType:      examInfo.ExamType,
			ReportUrl:     "", // 阶段测试暂不支持报告
			HasAnswered:   tAnswerTime > 0,
		}

		examList = append(examList, item)
	}

	return examList
}

// 辅助方法实现

// getPassRule 获取通过规则
// 对应PHP版本的_getPassRule方法
func (s examService) getPassRule(total int, pass interface{}) string {
	passStr := "-"
	if pass != nil {
		if passInt, ok := pass.(int); ok && passInt > 0 {
			passStr = strconv.Itoa(passInt)
		}
	}

	if passStr == "-" {
		return fmt.Sprintf("满分%d分", total)
	} else {
		return fmt.Sprintf("合格%s分/满分%d分", passStr, total)
	}
}

// getMaxAnswerTime 获取最大答题时间
// 对应PHP版本的_getMaxAnswerTime方法
func (s examService) getMaxAnswerTime(secondTime int) string {
	if secondTime <= 0 {
		return "-"
	}
	return fmt.Sprintf("%.0f分钟", float64(secondTime)/60)
}

// getAnswerTime 获取答题时间
// 对应PHP版本的_getAnswerTime方法
func (s examService) getAnswerTime(timeInSeconds int) string {
	if timeInSeconds <= 0 {
		return "-"
	}

	m := timeInSeconds / 60
	sec := timeInSeconds % 60
	var str string
	if m > 0 {
		str += fmt.Sprintf("%d分", m)
	}
	str += fmt.Sprintf("%d秒", sec)
	return str
}

// getRightCount 获取正确题目数量
// 对应PHP版本的_getRightCount方法
func (s examService) getRightCount(answerList []examcore.AnswerListItem) interface{} {
	if len(answerList) == 0 {
		return "-"
	}

	count := 0
	for _, item := range answerList {
		if item.Correct == 1 {
			count++
			continue
		}

		// 检查judgeInfo.detail
		if item.JudgeInfo != nil {
			if detail, exists := item.JudgeInfo["detail"]; exists {
				flag := true
				if detailSlice, ok := detail.([]interface{}); ok {
					for _, one := range detailSlice {
						var curCorrectRet int
						if item.Type != 53 {
							if oneMap, ok := one.(map[string]interface{}); ok {
								if correctRet, exists := oneMap["correctRet"]; exists {
									if correctRetInt, ok := correctRet.(int); ok {
										curCorrectRet = correctRetInt
									}
								}
							}
						} else {
							if oneMap, ok := one.(map[string]interface{}); ok {
								for _, tmp := range oneMap {
									if tmpMap, ok := tmp.(map[string]interface{}); ok {
										if correctRet, exists := tmpMap["correctRet"]; exists {
											if correctRetInt, ok := correctRet.(int); ok {
												curCorrectRet = correctRetInt
											}
										}
									}
									break
								}
							}
						}
						if curCorrectRet != 1 {
							flag = false
							break
						}
					}
				}
				if flag {
					count++
				}
			}
		}
	}
	return count
}

// getScore 获取分数
// 对应PHP版本的逻辑：isset($answerInfo['score']) ? $answerInfo['score'] : '-'
func (s examService) getScore(answerInfo examcore.AnswerInfo, hasAnswer bool) interface{} {
	if !hasAnswer {
		return "-"
	}
	// 如果有答案记录，返回分数（可能为0）
	return answerInfo.Score
}

// getDepartment 获取学段
// 对应PHP版本的_getDepartment方法
func (s examService) getDepartment(grade int) int {
	// 对应PHP版本的年级数组定义
	primary := []int{11, 12, 13, 14, 15, 16} // 小学
	middle := []int{2, 3, 4}                 // 初中
	high := []int{5, 6, 7}                   // 高中
	before := []int{60, 61, 62, 63, 64}      // 学前

	// 高中
	for _, g := range high {
		if g == grade {
			return 4
		}
	}

	// 初中
	for _, g := range middle {
		if g == grade {
			return 3
		}
	}

	// 小学
	for _, g := range primary {
		if g == grade {
			return 2
		}
	}

	// 学前
	for _, g := range before {
		if g == grade {
			return 1
		}
	}

	return 0 // 其他情况
}

// getPassScore 获取摸底测试通过分数
// 对应PHP版本的_getPassScore方法
func (s examService) getPassScore(urgeBottomTestRelation map[string]map[int64]examcore.BindInfo, urgeBottomTestBindStr string) interface{} {
	if bindInfos, exists := urgeBottomTestRelation[urgeBottomTestBindStr]; exists {
		for _, bindInfo := range bindInfos {
			if bindInfo.UserKv != nil {
				if passScore, exists := bindInfo.UserKv["passScore"]; exists {
					return passScore
				}
			}
		}
	}
	return nil
}

// getPassScoreFromStageRelation 获取阶段测试通过分数
// 对应PHP版本的_getPassScore方法（阶段测试版本）
func (s examService) getPassScoreFromStageRelation(stageRelation map[string]map[int64]examcore.BindInfo, bindKey string) interface{} {
	if bindInfos, exists := stageRelation[bindKey]; exists {
		for _, bindInfo := range bindInfos {
			if bindInfo.UserKv != nil {
				if passScore, exists := bindInfo.UserKv["passScore"]; exists {
					return passScore
				}
			}
		}
	}
	return nil
}

// extractLessonIdFromBindKey 从bindKey中提取lessonId
// 对应PHP版本的逻辑
func (s examService) extractLessonIdFromBindKey(bindKey string) int {
	// bindKey格式：lesson_123:9
	parts := strings.Split(bindKey, ":")
	if len(parts) > 0 {
		lessonPart := parts[0]
		if strings.HasPrefix(lessonPart, "lesson_") {
			lessonIdStr := strings.TrimPrefix(lessonPart, "lesson_")
			if lessonId, err := strconv.Atoi(lessonIdStr); err == nil {
				return lessonId
			}
		}
	}
	return 0
}

// isGradeSupported 检查年级是否支持报告
// 对应PHP版本的逻辑：摸底测报告只支持小学（包括学前）
func (s examService) isGradeSupported(grade int) bool {
	// 对应PHP版本：in_array($tmp['grade'], [11, 12, 13, 14, 15, 16, 60, 61, 62, 63, 64])
	supportedGrades := []int{11, 12, 13, 14, 15, 16, 60, 61, 62, 63, 64}
	for _, g := range supportedGrades {
		if g == grade {
			return true
		}
	}
	return false
}

// getShortUrl 获取短链接
// 对应PHP版本的Api_Su::getShortUrl调用
func (s examService) getShortUrl(ctx *gin.Context, url string) string {
	if url == "" {
		return ""
	}

	// 调用moat短链接服务
	moatClient := moat.NewClient()
	shortUrl, err := moatClient.GetShortUrl(ctx, url)
	if err != nil {
		zlog.Warnf(ctx, "getShortUrl 调用失败: url=%s, err=%v", url, err)
		return url // 失败时返回原链接
	}

	zlog.Debugf(ctx, "getShortUrl 成功: url=%s, shortUrl=%s", url, shortUrl)
	return shortUrl
}

// getCourseMapKeys 获取课程map的所有key（辅助调试函数）
func getCourseMapKeys(m map[string]dal.CourseInfo) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}
