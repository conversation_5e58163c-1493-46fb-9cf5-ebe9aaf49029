package ui

import (
	"bytes"
	"deskcrm/api/allocate"
	"deskcrm/api/dal"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/innerapi/input/inputKeepDetail"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/controllers/http/ui/output/outputStudent"
	"deskcrm/service/arkBase/arkConfig"
	"deskcrm/service/innerapi"
	"deskcrm/service/innerapi/courseRecord"
	"deskcrm/stru/ark"
	"deskcrm/stru/keepDetail"
	"deskcrm/util"
	"encoding/csv"
	"fmt"
	"net/http"
	"sort"
	"strconv"
	"time"

	commonArkGo "git.zuoyebang.cc/fwyybase/fwyylibs/api/arkgo"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// performanceService 学生课程表现数据服务
// 对应 PHP Service_Page_DeskV1_Student_PerformanceV1
type performanceService struct{}

var (
	PerformanceService performanceService
)

// GetPerformanceV1 获取学生课程表现数据
func (s performanceService) GetPerformanceV1(ctx *gin.Context, param *inputStudent.PerformanceV1Param) (rsp outputStudent.PerformanceV1Output, err error) {
	// 参数预处理和验证
	if err = s.validateAndPreprocess(ctx, param); err != nil {
		return
	}

	// 试卷数据分支处理
	if param.Tab == consts.TAB_EXAM {
		return s.handleExamData(ctx, param)
	}

	// 初始化基础数据
	basicData, err := s.initBasicData(ctx, param)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 初始化基础数据失败: %v", err)
		return
	}

	// 设置表头配置
	headers, err := s.processTableHeaders(ctx, basicData)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 设置表头失败: %v", err)
		return
	}

	// 格式化表格数据
	tableData, err := s.processTableData(ctx, basicData)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 格式化数据失败: %v", err)
		return
	}

	// 导出处理分支
	if param.IsExport {
		return s.handleExport(ctx, basicData, tableData, headers)
	}

	// 构建响应结果
	schemaId := cast.ToString(basicData.CourseRecordConfig.SchemaID)
	rsp = outputStudent.PerformanceV1Output{
		SchemaId:    &schemaId,
		TableData:   tableData,
		TableHeader: headers,
	}

	return
}

// validateAndPreprocess 参数验证与预处理
func (s performanceService) validateAndPreprocess(ctx *gin.Context, param *inputStudent.PerformanceV1Param) error {
	// leadsId 获取逻辑
	if param.LeadsId == 0 {
		// 如果 leadsId 为空，尝试获取
		leadsInfo, err := allocate.NewClient().GetLeadsByBatchCourseIdUid(ctx, []int64{param.CourseId}, param.StudentUid)
		if err != nil {
			zlog.Error(ctx, "GetLeadsByBatchCourseIdUid failed: %v", err)
			return err
		}
		param.LeadsId = leadsInfo[0].LeadsId
	}

	return nil
}

// handleExamData 处理试卷数据分支
// 对应 PHP 版本的试卷数据处理逻辑
func (s performanceService) handleExamData(ctx *gin.Context, param *inputStudent.PerformanceV1Param) (outputStudent.PerformanceV1Output, error) {
	zlog.Infof(ctx, "PerformanceV1 处理试卷数据: studentUid=%d, courseId=%d", param.StudentUid, param.CourseId)

	examList, err := innerapi.ExamService.GetExamTestList(ctx, param.StudentUid, param.CourseId)
	if err != nil {
		zlog.Errorf(ctx, "GetExamTestList failed: %v", err)
		return outputStudent.PerformanceV1Output{}, err
	}

	// 添加详细日志
	zlog.Infof(ctx, "handleExamData GetExamTestList 返回结果数量: %d", len(examList))
	if len(examList) == 0 {
		zlog.Warnf(ctx, "handleExamData GetExamTestList 返回空结果: studentUid=%d, courseId=%d", param.StudentUid, param.CourseId)
	}

	// 转换examList为[]any类型
	tableData := make([]outputStudent.LessonTableRow, len(examList))
	for i, item := range examList {
		mapData, err := util.StructToMap(item)
		if err != nil {
			zlog.Errorf(ctx, "StructToMap failed: %v", err)
			return outputStudent.PerformanceV1Output{}, err
		}
		tableData[i] = mapData
	}

	tableHeaders := s.setHeader(consts.GetExamDataHeaders())

	return outputStudent.PerformanceV1Output{
		TableData:   tableData,
		TableHeader: tableHeaders,
	}, nil
}

// initBasicData 初始化基础数据
func (s performanceService) initBasicData(ctx *gin.Context, param *inputStudent.PerformanceV1Param) (*keepDetail.BasicData, error) {
	zlog.Infof(ctx, "PerformanceV1 初始化基础数据: courseId=%d", param.CourseId)

	basicData := &keepDetail.BasicData{
		CourseId:           param.CourseId,
		LessonIds:          []int64{},
		StudentId:          param.StudentUid,
		CourseLessonInfos:  dal.CourseInfo{},
		CourseRecordConfig: keepDetail.CourseSchema{},
		FuncFieldKeys:      []string{},
		ArkFieldKeys:       []string{},
		AssistantUid:       param.AssistantUid,
		PersonUid:          param.PersonUid,
		LeadsId:            param.LeadsId,
	}

	courseInfo, err := dal.GetKVByCourseId(ctx, param.CourseId)
	if err != nil {
		zlog.Errorf(ctx, "GetCourseInfo failed: %v", err)
		return nil, err
	}

	if courseInfo, ok := courseInfo[cast.ToString(param.CourseId)]; ok {
		basicData.CourseLessonInfos = courseInfo
		for _, lessonInfo := range courseInfo.LessonList {
			basicData.LessonIds = append(basicData.LessonIds, int64(lessonInfo.LessonId))
		}
	}

	courseRecordConfig, err := courseRecord.NewConfigServiceCourseRecord().GetShouldersAndTagsAndTabsByCourseId(ctx, param.CourseId)
	if err != nil {
		zlog.Errorf(ctx, "GetShouldersAndTagsAndTabsByCourseId failed: %v", err)
		return nil, err
	}
	basicData.CourseRecordConfig = courseRecordConfig

	// 获取当前tab的表头配置
	tabConfig, exists := basicData.CourseRecordConfig.TabData[param.Tab]
	if !exists {
		zlog.Errorf(ctx, "PerformanceV1 未找到tab配置: %s", param.Tab)
		return nil, fmt.Errorf("核心数据未配置")
	}
	for _, header := range tabConfig.Headers {
		if _, exists := consts.HeaderMap[header.Value]; exists {
			basicData.FuncFieldKeys = append(basicData.FuncFieldKeys, header.Value)
		} else {
			basicData.ArkFieldKeys = append(basicData.ArkFieldKeys, header.Value)
		}
	}
	basicData.Tab = param.Tab
	zlog.Infof(ctx, "PerformanceV1 initBasicData: %+v", basicData)

	return basicData, nil
}

// processTableHeaders 设置表头配置
func (s performanceService) processTableHeaders(ctx *gin.Context, basicData *keepDetail.BasicData) ([]outputStudent.TableHeaderItem, error) {
	outputHeaders := make([]outputStudent.TableHeaderItem, 0)
	tableHeaders := basicData.CourseRecordConfig.TabData[basicData.Tab].Headers
	arkFieldKeys := basicData.ArkFieldKeys

	// 遍历每个表头配置项
	for _, tableHeader := range tableHeaders {
		// 尝试从标准表头映射中获取配置
		if headerItem, exists := consts.HeaderMap[tableHeader.Value]; exists {
			// 用标准表头映射构建表头项
			tableHeaderItem := outputStudent.TableHeaderItem{
				Label:   headerItem.Label,
				Prop:    headerItem.Prop,
				Cname:   headerItem.CName,
				Width:   headerItem.Width,
				Hover:   headerItem.Hover,
				Sort:    headerItem.Sort,
				Fixed:   headerItem.Fixed,
				Tooltip: headerItem.Tooltip,
				Remark:  headerItem.Remark,
			}

			// 应用自定义配置
			if tableHeader.CustomName != "" {
				tableHeaderItem.Label = tableHeader.CustomName
			}
			if tableHeader.Width != "" {
				tableHeaderItem.Width = cast.ToInt(tableHeader.Width)
			}
			if tableHeader.Hover != "" {
				tableHeaderItem.Hover = tableHeader.Hover
			}

			tableHeaderItem.Tooltip = true

			outputHeaders = append(outputHeaders, tableHeaderItem)
		}
	}

	// 处理方舟字段
	if len(arkFieldKeys) > 0 {
		arkHeaders, err := s.getArkHeaders(ctx, arkFieldKeys)
		if err != nil {
			zlog.Errorf(ctx, "PerformanceV1 获取方舟表头失败: %v", err)
			return nil, err
		}
		outputHeaders = append(outputHeaders, arkHeaders...)
	}

	components.Debugf(ctx, "PerformanceV1 processTableHeaders outputHeaders: %+v", outputHeaders)
	return outputHeaders, nil
}

// processTableData 格式化表格数据
func (s performanceService) processTableData(ctx *gin.Context, basicData *keepDetail.BasicData) (result []outputStudent.LessonTableRow, err error) {
	funcFieldKeys := basicData.FuncFieldKeys
	arkFieldKeys := basicData.ArkFieldKeys

	// 1. 调用 KeepDetailService.LessonList 获取课程数据
	var funcData map[int64]map[string]interface{}
	if len(funcFieldKeys) > 0 {
		funcData, err = s.fetchFuncData(ctx, basicData, funcFieldKeys)
		if err != nil {
			zlog.Errorf(ctx, "fetchLessonData failed: %v", err)
			return nil, err
		}
	}

	// 2. 根据 ArkFieldKeys 获取方舟数据
	var arkData map[int64]map[string]interface{}
	if len(arkFieldKeys) > 0 {
		arkData, err = s.fetchArkData(ctx, basicData, arkFieldKeys)
		if err != nil {
			zlog.Errorf(ctx, "fetchArkData failed: %v", err)
			return nil, err
		}
	}

	// 3. 合并结果
	result, err = s.mergeTableData(ctx, basicData, funcData, arkData)
	if err != nil {
		zlog.Errorf(ctx, "mergeTableData failed: %v", err)
		return nil, err
	}

	components.Debugf(ctx, "PerformanceV1 processTableData result: %+v", result)

	return result, nil
}

// fetchFuncData 调用 KeepDetailService.LessonList 获取课程数据
func (s performanceService) fetchFuncData(ctx *gin.Context, basicData *keepDetail.BasicData, funcFieldKeys []string) (map[int64]map[string]interface{}, error) {
	// 一些不知道有没有用到的字段，为了和PHP保持一致
	funcFieldKeys = append(funcFieldKeys, "lpcStrengthPracticeStatus", "inClassHandsUpNum", "inClassVideoLinkNum", "teacherName", "lessonStartStatus", "hasQuestion", "hasPreview")

	// 构建参数
	lessonParam := &inputKeepDetail.LessonListParam{
		AssistantUid:  basicData.AssistantUid,
		PersonUid:     basicData.PersonUid,
		CourseId:      basicData.CourseId,
		StudentUid:    basicData.StudentId,
		LeadsId:       basicData.LeadsId,
		NeedFieldKeys: funcFieldKeys,
		Offset:        0,
		Limit:         10000, // 获取所有数据
	}

	// 调用 KeepDetailService.LessonList
	resp, err := innerapi.KeepDeatilService.LessonList(ctx, lessonParam)
	if err != nil {
		return nil, fmt.Errorf("KeepDetailService.LessonList failed: %w", err)
	}

	// 转换成map
	funcData := make(map[int64]map[string]interface{})
	for _, lesson := range resp.LessonList {
		if lessonIdVal, exists := lesson["lessonId"]; exists {
			if lessonId, ok := lessonIdVal.(int64); ok {
				funcData[lessonId] = lesson
			}
		}
	}

	components.Debugf(ctx, "PerformanceV1 fetchFuncData funcData: %+v", funcData)

	return funcData, nil
}

// fetchArkData 根据 ArkFieldKeys 获取方舟数据
func (s performanceService) fetchArkData(ctx *gin.Context, basicData *keepDetail.BasicData, arkFieldKeys []string) (map[int64]map[string]interface{}, error) {
	arkData := make(map[int64]map[string]interface{})
	for _, lessonId := range basicData.LessonIds {
		// 使用 commonArkGo.ArkFormat 获取方舟数据
		formatData, err := commonArkGo.ArkFormat(ctx, commonArkGo.ArkFormatReq{
			AssistantUid:         basicData.AssistantUid,
			PersonUid:            basicData.PersonUid,
			CourseId:             basicData.CourseId,
			LessonId:             lessonId,
			StudentUids:          []int64{basicData.StudentId},
			LeadsIdMapStudentUid: map[int64]int64{basicData.LeadsId: basicData.StudentId},
			FormatKeys:           arkFieldKeys,
		})
		if err != nil {
			return nil, err
		}
		if studentData, exists := formatData.StudentList[basicData.StudentId]; exists {
			arkData[lessonId] = studentData
		}
	}

	components.Debugf(ctx, "PerformanceV1 fetchArkData arkData: %+v", arkData)

	return arkData, nil
}

// mergeTableData 合并课程数据和方舟数据
func (s performanceService) mergeTableData(ctx *gin.Context, basicData *keepDetail.BasicData, funcData map[int64]map[string]interface{}, arkData map[int64]map[string]interface{}) ([]outputStudent.LessonTableRow, error) {
	var tableData []outputStudent.LessonTableRow

	for _, lessonInfo := range basicData.CourseLessonInfos.LessonList {
		row := outputStudent.LessonTableRow{}
		lessonId := int64(lessonInfo.LessonId)

		// 设置基础字段
		row["lessonId"] = lessonId
		row["type"] = lessonInfo.LessonType
		row["lessonType"] = 1
		row["playType"] = lessonInfo.PlayType
		row["startTime"] = lessonInfo.StartTime
		row["stopTime"] = lessonInfo.StopTime
		row["inclassTime"] = time.Unix(int64(lessonInfo.StartTime), 0).Format("2006-01-02 15:04") + "-" + time.Unix(int64(lessonInfo.StopTime), 0).Format("15:04")
		row["lessonReportUrl"] = []string{}
		row["lessonReportStatus"] = 0
		row["hasQuestion"] = 0
		row["hasPreview"] = 0

		// 兼容PHP getLpcLessonType 逻辑
		if lessonInfo.PlayType == dal.PLAY_TYPE_AI_INTERACT || lessonInfo.PlayType == dal.PLAY_TYPE_AI_HUDONG {
			row["lessonType"] = 2
		}

		// 合并字段映射函数数据
		if lessonFuncData, exists := funcData[lessonId]; exists {
			for key, value := range lessonFuncData {
				row[key] = value
			}
		}

		// 合并方舟数据
		if lessonArkData, exists := arkData[lessonId]; exists {
			for key, value := range lessonArkData {
				row[key] = value
			}
		}

		// 特殊逻辑处理
		if lessonInfo.T007Tag == 1 {
			if row["exerciseAll"] == "-" {
				row["exerciseAll"] = "查看详情"
			}
			row["isShowExerciseAllDetail"] = 1
		}

		delete(row, "courseId")

		tableData = append(tableData, row)
	}

	// 结果根据startTime排序
	sort.Slice(tableData, func(i, j int) bool {
		startTimeI, okI := tableData[i][consts.HEADER_STARTTIME].(int)
		startTimeJ, okJ := tableData[j][consts.HEADER_STARTTIME].(int)
		if okI && okJ {
			return startTimeI < startTimeJ
		}
		return false
	})

	return tableData, nil
}

// handleExport 处理导出功能
func (s performanceService) handleExport(ctx *gin.Context, basicData *keepDetail.BasicData, tableData []outputStudent.LessonTableRow, headers []outputStudent.TableHeaderItem) (outputStudent.PerformanceV1Output, error) {
	lessonList, err := s.doExport(ctx, tableData, headers)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 doExport 失败: %v", err)
		return outputStudent.PerformanceV1Output{}, err
	}

	err = s.genCsv(ctx, lessonList)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 genCsv 失败: %v", err)
		return outputStudent.PerformanceV1Output{}, err
	}

	ctx.Abort()

	// 这里不会执行到，因为已经 Abort 了
	return outputStudent.PerformanceV1Output{}, nil
}

func (s performanceService) setHeader(keys []string) []outputStudent.TableHeaderItem {
	tableHeaders := make([]outputStudent.TableHeaderItem, 0, len(keys))
	for _, headerKey := range keys {
		if headerItem, exists := consts.HeaderMap[headerKey]; exists {
			tableHeaders = append(tableHeaders, outputStudent.TableHeaderItem{
				Label: headerItem.Label,
				Prop:  headerItem.Prop,
				Cname: headerItem.CName,
				Width: headerItem.Width,
				Hover: headerItem.Hover,
				Sort:  headerItem.Sort,
			})
		}
	}
	return tableHeaders
}

// getArkHeaders 获取方舟字段的表头配置
// 对应 PHP 版本的方舟规则处理逻辑
func (s performanceService) getArkHeaders(ctx *gin.Context, arkKeys []string) ([]outputStudent.TableHeaderItem, error) {
	zlog.Infof(ctx, "PerformanceV1 获取方舟表头: keys=%v", arkKeys)

	// 获取所有字段规则
	arkFieldRules, err := arkConfig.GetRuleConfigInstance(ctx).GetFieldRuleByKeys(ctx, arkKeys)
	if err != nil {
		zlog.Errorf(ctx, "PerformanceV1 获取方舟字段规则失败: %v", err)
		return nil, err
	}

	var arkHeaders []outputStudent.TableHeaderItem

	// 遍历方舟键，查找匹配的规则
	for _, arkKey := range arkKeys {
		if fieldRule, exists := arkFieldRules[arkKey]; exists {
			// 检查规则是否适用于 ARK_APP_DETAIL_COLUMN 应用
			if s.isRuleForKeepDetail(fieldRule.Apps, consts.ARK_APP_DETAIL_COLUMN) {
				// 构建方舟表头项
				arkHeader := outputStudent.TableHeaderItem{
					Label:   fieldRule.CustomName,
					Prop:    arkKey,
					Cname:   "CommonText",
					Width:   s.getArkHeaderWidth(fieldRule.FeConfig),
					Hover:   "",
					Sort:    0,
					Fixed:   false,
					Tooltip: true,
					Remark:  "方舟",
				}

				arkHeaders = append(arkHeaders, arkHeader)
			}
		}
	}

	zlog.Infof(ctx, "PerformanceV1 方舟表头获取完成，共%d个", len(arkHeaders))
	return arkHeaders, nil
}

// isRuleForKeepDetail 检查方舟规则是否用于维系详情
func (s performanceService) isRuleForKeepDetail(apps []int64, appId int64) bool {
	for _, app := range apps {
		if app == appId {
			return true
		}
	}
	return false
}

// getArkHeaderWidth 从方舟规则的前端配置中获取宽度
func (s performanceService) getArkHeaderWidth(feConfig ark.FeConfigStu) int {
	if feConfig.Width > 0 {
		return int(feConfig.Width)
	}
	return 100 // 默认宽度
}

// doExport 导出数据处理方法
func (s performanceService) doExport(ctx *gin.Context, tableData []outputStudent.LessonTableRow, headers []outputStudent.TableHeaderItem) ([][]string, error) {
	// 提取表头标题
	titles := make([]string, 0, len(headers))
	for _, header := range headers {
		titles = append(titles, header.Label)
	}

	lessonList := make([][]string, 0)

	sort.Slice(tableData, func(i, j int) bool {
		startTimeI, okI := tableData[i]["startTime"].(int)
		startTimeJ, okJ := tableData[j]["startTime"].(int)
		if okI && okJ {
			return startTimeI < startTimeJ
		}
		return false
	})

	for _, val := range tableData {
		row := make([]string, 0, len(headers))
		for _, header := range headers {
			prop := header.Prop

			// 处理特殊字段
			if s.isSpecialArrayField(prop) {
				if arrayVal, exists := val[prop]; exists {
					if arr, ok := arrayVal.([]interface{}); ok && len(arr) > 0 {
						row = append(row, cast.ToString(arr[0]))
					} else {
						row = append(row, "")
					}
				} else {
					row = append(row, "")
				}
				continue
			}

			// 处理时间字段
			if prop == "startTime" {
				if timeVal, exists := val[prop]; exists {
					if timestamp, ok := timeVal.(int64); ok {
						row = append(row, time.Unix(timestamp, 0).Format("2006-01-02 15:04:05"))
					} else {
						row = append(row, "")
					}
				} else {
					row = append(row, "")
				}
				continue
			}

			// 处理学习计划标签
			if prop == "studyPlanTag" {
				if tagVal, exists := val[prop]; exists {
					if tagInt := cast.ToInt(tagVal); tagInt > 0 {
						if tagText, exists := consts.GJKTagMap[tagInt]; exists {
							row = append(row, tagText)
						} else {
							row = append(row, cast.ToString(tagVal))
						}
					} else {
						row = append(row, cast.ToString(tagVal))
					}
				} else {
					row = append(row, "")
				}
				continue
			}

			if fieldVal, exists := val[prop]; exists {
				row = append(row, cast.ToString(fieldVal))
			} else {
				row = append(row, "")
			}
		}

		lessonList = append(lessonList, row)
	}

	// 将标题行添加到最前面
	result := make([][]string, 0, len(lessonList)+1)
	result = append(result, titles)
	result = append(result, lessonList...)

	components.Debugf(ctx, "PerformanceV1 doExport lessonList: %+v", result)

	return result, nil
}

// isSpecialArrayField 判断是否为特殊数组字段
func (s performanceService) isSpecialArrayField(prop string) bool {
	specialFields := []string{"preview", "inclassTest", "homework", "oralQuestion", "similarHomework"}
	for _, field := range specialFields {
		if prop == field {
			return true
		}
	}
	return false
}

// genCsv 生成 CSV 文件并直接输出
func (s performanceService) genCsv(ctx *gin.Context, data [][]string) error {
	var buf bytes.Buffer
	buf.Write([]byte{0xEF, 0xBB, 0xBF})
	writer := csv.NewWriter(&buf)

	for _, row := range data {
		if err := writer.Write(row); err != nil {
			return fmt.Errorf("写入 CSV 行失败: %w", err)
		}
	}

	// 刷新 writer
	writer.Flush()
	if err := writer.Error(); err != nil {
		return fmt.Errorf("CSV writer 错误: %w", err)
	}

	fileName := fmt.Sprintf("_%s.csv", time.Now().Format("200601021504"))

	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Accept-Ranges", "bytes")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	ctx.Header("Content-Length", strconv.Itoa(buf.Len()))

	ctx.Data(http.StatusOK, "application/octet-stream", buf.Bytes())

	return nil
}
