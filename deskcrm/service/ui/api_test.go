package ui

import (
	"deskcrm/api/dal"
	"deskcrm/components"
	"deskcrm/consts"
	"deskcrm/controllers/http/ui/input/inputStudent"
	"deskcrm/helpers"
	courseRecord2 "deskcrm/service/innerapi/courseRecord"
	"deskcrm/stru/keepDetail"
	"fmt"
	"net/http"
	"net/http/httptest"
	"path"
	"reflect"
	"runtime"
	"testing"
	"time"

	"bou.ke/monkey"
	commonArkGo "git.zuoyebang.cc/fwyybase/fwyylibs/api/arkgo"
	baseTower "git.zuoyebang.cc/fwyybase/fwyylibs/api/tower"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/assert"
)

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../..")

	helpers.PreInit()
	helpers.InitMysql()
	helpers.InitValidator()
	helpers.InitApiClient()
	helpers.InitRedis()
}

func createCtx() *gin.Context {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)
	req, _ := http.NewRequest("GET", "/", nil)
	req.AddCookie(&http.Cookie{
		Name:  "ZYBIPSCAS",
		Value: "IPS_29d88adfe87f4d56346cd7244b0154a01754966600",
	})

	req.AddCookie(&http.Cookie{
		Name:  "XUID",
		Value: "2136917703",
	})
	ctx.Request = req
	ctx.Set("isDebug", true)
	return ctx
}

func TestStudentService_StudentDetailV1(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	param := &inputStudent.StudentDetailV1Param{
		StudentUid:   2285235952,
		CourseId:     3067958,
		LeadsId:      1561215123,
		AssistantUid: 4312624260,
		PersonUid:    2136917703,
		Tags:         "tagUserType,tagIsTransferStudent,tagGuardianWechatLight,tagServiceType",
		TagArr:       []string{"tagUserType", "tagIsTransferStudent", "tagGuardianWechatLight", "tagServiceType"},
	}

	// 先不测走方舟规则的这种
	mockRules := &commonArkGo.GetFieldRuleByKeysResp{
		"tag1": {},
		"tag2": {},
	}

	patch1 := monkey.Patch(commonArkGo.GetFieldRuleByKeys,
		func(ctx *gin.Context, param commonArkGo.GetFieldRuleByKeysParam) (*commonArkGo.GetFieldRuleByKeysResp, error) {
			return mockRules, nil
		})
	defer patch1.Unpatch()

	service := studentService{}
	resp, err := service.StudentDetailV1(ctx, param)

	components.DebugfWithJSON(ctx, "StudentDetailV1 resp: %s", resp)

	assert.Nil(t, err)
	assert.NotEmpty(t, resp)
}

func TestStudentService_CourseRecordV2(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	p := &inputStudent.CourseRecordV2Param{
		StudentUid:        2285235952,
		CourseId:          3067958,
		LeadsId:           1561215123,
		Category:          3,
		OnlyCurrentCourse: 1,
		AssistantUid:      4312624260,
		PersonUid:         2136917703,
	}

	if p.Year == 0 {
		p.Year = consts.YearAll
	}

	if p.Season == 0 {
		p.Season = consts.SeasonAll
	}

	if p.BuyType == 0 {
		p.BuyType = consts.BuyAll
	}

	if len(p.NewCourseTypes) > 0 {
		err := jsoniter.UnmarshalFromString(p.NewCourseTypes, &p.NewCourseTypesArr)
		if err != nil {
			zlog.Error(ctx, "UnmarshalFromString err: %v", err)
			return
		}
	}

	if p.Year == consts.YearAll {
		now := time.Now().Unix()
		p.StartTime = now - 365*86400
		p.EndTime = now
	} else {
		timeStr := fmt.Sprintf("%d-01-01", p.Year)
		yearStart, _ := time.Parse("2006-01-02", timeStr)
		p.StartTime = yearStart.Unix() - 180*86400
		p.EndTime = yearStart.Unix() + 365*86400
	}

	// 先不测走方舟规则的这种
	mockRules := &commonArkGo.GetFieldRuleByAppResp{
		"tag1": {},
		"tag2": {},
	}

	patch1 := monkey.Patch(commonArkGo.GetFieldRuleByApp,
		func(ctx *gin.Context, param commonArkGo.GetFieldRuleByAppParam) (*commonArkGo.GetFieldRuleByAppResp, error) {
			return mockRules, nil
		})
	defer patch1.Unpatch()

	mockService := &courseRecord2.ConfigCourseRecordService{}
	patch2 := monkey.Patch(courseRecord2.NewConfigServiceCourseRecord,
		func() *courseRecord2.ConfigCourseRecordService {
			return mockService
		})

	patch3 := monkey.PatchInstanceMethod(reflect.TypeOf(mockService), "GetShouldersAndTagsAndTabsByCourseIds",
		func(cs *courseRecord2.ConfigCourseRecordService, ctx *gin.Context, courseList []dal.CourseInfo, towerCourseMap map[int64]baseTower.GetCourseInfoRsp) (keepDetail.CourseSchemaResponse, error) {
			mockResponse := keepDetail.CourseSchemaResponse{
				3067958: keepDetail.CourseSchema{
					Shoulders: []string{
						"stageTest",
						"stageReport",
						"stageReportLpc",
						"studyReport",
						"checkTest",
						"totalScore",
						"bookSend",
						"examErrorButton",
						"errorTaskButton",
					},
					ShoulderTags: []string{
						"isL2rSpaceSeason",
						"hasLateEnrollee",
						"tagRefund",
					},
					TabData: map[string]keepDetail.TabHeaders{
						"coreData": {
							Headers: []keepDetail.Header{
								{
									Value:      "lessonName",
									Label:      "章节名称",
									Width:      "280",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "startTime",
									Label:      "上课时间",
									Width:      "140",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "attend",
									Label:      "到课",
									Width:      "80",
									Hover:      "学员若请假且未到课，则展现学员请假。若学员到课，则展示学员直播课环节中累计到课时长。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "exercise",
									Label:      "直播互动题",
									Width:      "90",
									Hover:      "展现学员直播过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
									Remark:     "通用",
									CustomName: "",
								},
								{
									Value:      "exerciseAll",
									Label:      "观看互动题",
									Width:      "90",
									Hover:      " 展现学员直播+回放过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
									Remark:     "通用",
									CustomName: "",
								},
								{
									Value:      "playbackv1",
									Label:      "回放观看时长(新)",
									Width:      "80",
									Hover:      "展示的是回放或LBP的累计观看时长。前60天实时更新，60天之后的数据每日更新一次",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "homework",
									Label:      "巩固练习",
									Width:      "80",
									Hover:      "普通课程巩固练习作答评级，分为S/A/B三类。ilab课程展现学员巩固练习成绩，分为优秀良好一般三类",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "preview",
									Label:      "预习",
									Width:      "80",
									Hover:      "普通课程展现作答正确数、作答数、试卷题目总数。ilab课程展现优秀、良好、一般三类评级。",
									Remark:     "原辅导",
									CustomName: "",
								},
							},
						},
						"ktbx": {
							Headers: []keepDetail.Header{
								{
									Value:      "lessonName",
									Label:      "章节名称",
									Width:      "300",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "startTime",
									Label:      "上课时间",
									Width:      "200",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "talk",
									Label:      "课中聊天",
									Width:      "100",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "monthlyExamReport",
									Label:      "考试报告",
									Width:      "100",
									Hover:      "需在学员提交堂堂测之后才会生成考试报告哦。",
									Remark:     "原辅导",
									CustomName: "",
								},
							},
						},
						"mryl": {
							Headers: []keepDetail.Header{},
						},
					},
					SchemaID: 43,
				},
			}
			return mockResponse, nil
		})
	defer patch2.Unpatch()
	defer patch3.Unpatch()

	// 执行被测试的方法
	service := studentService{}
	resp, err := service.CourseRecordV2(ctx, p)

	// 输出调试信息
	components.DebugfWithJSON(ctx, "CourseRecordV2 resp: %s", resp)

	// 断言测试结果
	assert.Nil(t, err)
	assert.NotNil(t, resp)
}

func TestPerformanceService_GetPerformanceV1(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	p := &inputStudent.PerformanceV1Param{
		StudentUid:   2285235952,
		CourseId:     3067958,
		LeadsId:      1561215123,
		Tab:          consts.TAB_CORE_DATA,
		AssistantUid: 4312624260,
		PersonUid:    2136917703,
		IsExport:     false,
	}

	// Mock方舟规则
	mockRules := &commonArkGo.GetFieldRuleByAppResp{
		"tag1": {},
		"tag2": {},
	}

	patch1 := monkey.Patch(commonArkGo.GetFieldRuleByApp,
		func(ctx *gin.Context, param commonArkGo.GetFieldRuleByAppParam) (*commonArkGo.GetFieldRuleByAppResp, error) {
			return mockRules, nil
		})
	defer patch1.Unpatch()

	// Mock课程记录配置服务
	mockService := &courseRecord2.ConfigCourseRecordService{}
	patch2 := monkey.Patch(courseRecord2.NewConfigServiceCourseRecord,
		func() *courseRecord2.ConfigCourseRecordService {
			return mockService
		})

	// Mock GetShouldersAndTagsAndTabsByCourseId方法
	patch3 := monkey.PatchInstanceMethod(reflect.TypeOf(mockService), "GetShouldersAndTagsAndTabsByCourseIds",
		func(cs *courseRecord2.ConfigCourseRecordService, ctx *gin.Context, courseList []dal.CourseInfo, towerCourseMap map[int64]baseTower.GetCourseInfoRsp) (keepDetail.CourseSchemaResponse, error) {
			mockResponse := keepDetail.CourseSchemaResponse{
				3067958: keepDetail.CourseSchema{
					Shoulders:    []string{},
					ShoulderTags: []string{},
					TabData: map[string]keepDetail.TabHeaders{
						"coreData": {
							Headers: []keepDetail.Header{
								{
									Value:      "lessonName",
									Label:      "章节名称",
									Width:      "280",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "startTime",
									Label:      "上课时间",
									Width:      "140",
									Hover:      "",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "attend",
									Label:      "到课",
									Width:      "80",
									Hover:      "学员若请假且未到课，则展现学员请假。若学员到课，则展示学员直播课环节中累计到课时长。",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "exercise",
									Label:      "直播互动题",
									Width:      "90",
									Hover:      "展现学员直播过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
									Remark:     "通用",
									CustomName: "",
								},
								{
									Value:      "exerciseAll",
									Label:      "观看互动题",
									Width:      "90",
									Hover:      " 展现学员直播+回放过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
									Remark:     "通用",
									CustomName: "",
								},
								{
									Value:      "playbackv1",
									Label:      "回放观看时长(新)",
									Width:      "80",
									Hover:      "展示的是回放或LBP的累计观看时长。前60天实时更新，60天之后的数据每日更新一次",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "homework",
									Label:      "巩固练习",
									Width:      "80",
									Hover:      "普通课程巩固练习作答评级，分为S/A/B三类。ilab课程展现学员巩固练习成绩，分为优秀良好一般三类",
									Remark:     "原辅导",
									CustomName: "",
								},
								{
									Value:      "preview",
									Label:      "预习",
									Width:      "80",
									Hover:      "普通课程展现作答正确数、作答数、试卷题目总数。ilab课程展现优秀、良好、一般三类评级。",
									Remark:     "原辅导",
									CustomName: "",
								},
							},
						},
					},
					SchemaID: 43,
				},
			}
			return mockResponse, nil
		})
	defer patch2.Unpatch()
	defer patch3.Unpatch()

	// 执行被测试的方法
	service := performanceService{}
	resp, err := service.GetPerformanceV1(ctx, p)

	// 输出调试信息
	components.DebugfWithJSON(ctx, "GetPerformanceV1 resp: %s", resp)

	// 断言测试结果
	assert.Nil(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "43", resp.SchemaId)
	assert.NotNil(t, resp.TableHeader)
	assert.NotNil(t, resp.TableData)
}

func TestPerformanceService_GetPerformanceV1_Exam(t *testing.T) {
	// 创建测试上下文
	gin.SetMode(gin.TestMode)
	ctx := createCtx()

	// 设置输入参数
	p := &inputStudent.PerformanceV1Param{
		StudentUid:   2285235952,
		CourseId:     3067958,
		LeadsId:      1561215123,
		Tab:          consts.TAB_EXAM,
		AssistantUid: 4312624260,
		PersonUid:    2136917703,
		IsExport:     false,
	}

	// 执行被测试的方法
	service := performanceService{}
	resp, err := service.GetPerformanceV1(ctx, p)

	// 输出调试信息
	components.DebugfWithJSON(ctx, "GetPerformanceV1 TableData: %s", resp.TableData)
	components.DebugfWithJSON(ctx, "GetPerformanceV1 TableHeader: %s", resp.TableHeader)

	// 断言测试结果
	assert.Nil(t, err)
	assert.NotNil(t, resp)
	assert.NotNil(t, resp.TableHeader)
	assert.NotNil(t, resp.TableData)
}
