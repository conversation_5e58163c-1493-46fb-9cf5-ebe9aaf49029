package examcore

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"deskcrm/util"
	"encoding/json"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

// Client examcore API客户端
type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.ExamCore,
	}
	return c
}

const (
	getAnswerApi   = "/examcore/v1/getanswer"   // 获取作答信息
	getRelationApi = "/examcore/v1/getrelation" // 获取绑定关系
	getExamApi     = "/examcore/v1/getexam"     // 获取试卷信息

	// 认证常量，对应PHP中的APP_ID和APP_SECRET
	appID     = 10000010
	appSecret = "22f0da0c"
)

// GetRelation 获取绑定关系 (分批调用，每批最多10个bindStr)
func (c *Client) GetRelation(ctx *gin.Context, bindList []string) (map[string]map[int64]BindInfo, error) {
	if len(bindList) == 0 {
		return make(map[string]map[int64]BindInfo), nil
	}

	// 分批处理，每批最多10个bindStr
	batchSize := 10
	result := make(map[string]map[int64]BindInfo)

	for i := 0; i < len(bindList); i += batchSize {
		end := min(i+batchSize, len(bindList))
		batchBindStrs := bindList[i:end]

		// 1. 构建请求参数
		req := map[string]interface{}{
			"appId":     appID,
			"appSecret": appSecret,
			"bindStrs":  batchBindStrs, // 使用当前批次的bindStr
		}

		// 2. 设置请求选项
		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
		utils.DecorateHttpOptions(ctx, &opts)

		// 3. 发送请求
		res, err := c.cli.HttpPost(ctx, getRelationApi, opts)
		if err != nil {
			zlog.Warnf(ctx, "ExamCore GetRelation request err (batch %d-%d): %v", i, end-1, err)
			return nil, err // 如果某一批次失败，则整个调用失败
		}

		// 4. 检查 HTTP 状态码
		if err = api.ApiHttpCode(ctx, res); err != nil {
			return nil, err
		}

		// 5. 解析响应
		var examcoreResp ExamcoreResponse
		if _, err = api.DecodeResponse(ctx, res, &examcoreResp); err != nil {
			return nil, err
		}

		// 解析data.list为目标格式
		var batchResp map[string]map[int64]BindInfo
		listBytes, err := json.Marshal(examcoreResp.List)
		if err != nil {
			zlog.Warnf(ctx, "ExamCore GetRelation marshal list err (batch %d-%d): %v", i, end-1, err)
			return nil, err
		}

		if err = json.Unmarshal(listBytes, &batchResp); err != nil {
			zlog.Warnf(ctx, "ExamCore GetRelation unmarshal list err (batch %d-%d): %v", i, end-1, err)
			return nil, err
		}

		// 将当前批次的结果合并到总结果中
		for k, v := range batchResp {
			result[k] = v
		}
	}

	return result, nil
}

// GetAnswer 获取学生试卷作答信息 (分批调用，每批最多50个answerKey)
func (c *Client) GetAnswer(ctx *gin.Context, answerKeyList []string) (map[string][]AnswerInfo, error) {
	if len(answerKeyList) == 0 {
		return make(map[string][]AnswerInfo), nil
	}

	// 分批处理，每批最多50个answerKey
	batchSize := 50
	result := make(map[string][]AnswerInfo)

	for i := 0; i < len(answerKeyList); i += batchSize {
		end := min(i+batchSize, len(answerKeyList))
		batchAnswerKeys := answerKeyList[i:end]

		// 1. 构建请求参数
		req := map[string]interface{}{
			"appId":      appID,
			"appSecret":  appSecret,
			"answerKeys": batchAnswerKeys, // 使用当前批次的answerKey
		}

		// 2. 设置请求选项
		opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
		utils.DecorateHttpOptions(ctx, &opts)

		// 3. 发送请求
		res, err := c.cli.HttpPost(ctx, getAnswerApi, opts)
		if err != nil {
			zlog.Warnf(ctx, "ExamCore GetAnswer request err (batch %d-%d): %v", i, end-1, err)
			return nil, err // 如果某一批次失败，则整个调用失败
		}

		// 4. 检查 HTTP 状态码
		if err = api.ApiHttpCode(ctx, res); err != nil {
			return nil, err
		}

		// 5. 解析响应
		var examcoreResp ExamcoreResponse
		if _, err = api.DecodeResponse(ctx, res, &examcoreResp); err != nil {
			return nil, err
		}

		// 解析data.list为目标格式
		var batchResp map[string][]AnswerInfo
		listBytes, err := json.Marshal(examcoreResp.List)
		if err != nil {
			zlog.Warnf(ctx, "ExamCore GetAnswer marshal list err (batch %d-%d): %v", i, end-1, err)
			return nil, err
		}

		if err = json.Unmarshal(listBytes, &batchResp); err != nil {
			zlog.Warnf(ctx, "ExamCore GetAnswer unmarshal list err (batch %d-%d): %v", i, end-1, err)
			return nil, err
		}

		// 将当前批次的结果合并到总结果中
		for k, v := range batchResp {
			result[k] = v
		}
	}

	return result, nil
}

// GetBottomTestSubjectList 获取摸底测学科列表
func (c *Client) GetBottomTestSubjectList(ctx *gin.Context, cpuId int64) (map[int64]int, error) {
	if cpuId == 0 {
		return make(map[int64]int), nil
	}

	// 1. 先获取绑定关系
	bindStr := FormatBindStr(cpuId, BindTypeCpu, ExamTypeSurvey)
	relations, err := c.GetRelation(ctx, []string{bindStr})
	if err != nil {
		return nil, err
	}

	bindRelations, exists := relations[bindStr]
	if !exists || len(bindRelations) == 0 {
		return make(map[int64]int), nil
	}

	// 2. 提取examId列表
	var examIds []int64
	for examId := range bindRelations {
		examIds = append(examIds, examId)
	}

	// 3. 获取试卷信息
	examList, err := c.GetExamInfo(ctx, examIds)
	if err != nil {
		return nil, err
	}

	// 4. 提取学科信息
	result := make(map[int64]int)
	for examId, examInfo := range examList {
		result[examId] = examInfo.Exam.Subject
	}

	return result, nil
}

// GetExamInfo 获取试卷信息
func (c *Client) GetExamInfo(ctx *gin.Context, examIds []int64) (map[int64]ExamDetailInfo, error) {
	if len(examIds) == 0 {
		return make(map[int64]ExamDetailInfo), nil
	}

	// 1. 构建请求参数
	req := map[string]interface{}{
		"appId":           appID,
		"appSecret":       appSecret,
		"examIds":         util.ConvertArrayIntToArrayString(examIds),
		"hasQuestionlist": 0, // 修复：使用正确的参数名称，与PHP版本保持一致
	}

	// 2. 设置请求选项
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeJson}
	utils.DecorateHttpOptions(ctx, &opts)

	// 3. 发送请求
	res, err := c.cli.HttpPost(ctx, getExamApi, opts)
	if err != nil {
		zlog.Warnf(ctx, "ExamCore GetExamInfo request err: %v", err)
		return nil, err
	}

	// 4. 检查 HTTP 状态码
	if err = api.ApiHttpCode(ctx, res); err != nil {
		return nil, err
	}

	// 5. 解析响应
	var examcoreResp ExamcoreResponse
	if _, err = api.DecodeResponse(ctx, res, &examcoreResp); err != nil {
		return nil, err
	}

	// 解析data.list为目标格式
	var result map[int64]ExamDetailInfo
	listBytes, err := json.Marshal(examcoreResp.List)
	if err != nil {
		zlog.Warnf(ctx, "ExamCore GetExamInfo marshal list err: %v", err)
		return nil, err
	}

	if err = json.Unmarshal(listBytes, &result); err != nil {
		zlog.Warnf(ctx, "ExamCore GetExamInfo unmarshal list err: %v", err)
		return nil, err
	}

	return result, nil
}
